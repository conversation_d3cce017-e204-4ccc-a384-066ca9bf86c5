# 🤝 Guia de Contribuição

Obrigado por considerar contribuir com o SysDelivery! Este documento fornece diretrizes para contribuições.

## 📋 Índice

- [Código de Conduta](#código-de-conduta)
- [Como Contribuir](#como-contribuir)
- [Padrões de Desenvolvimento](#padrões-de-desenvolvimento)
- [Processo de Pull Request](#processo-de-pull-request)
- [Reportar Bugs](#reportar-bugs)
- [Sugerir Funcionalidades](#sugerir-funcionalidades)

## 📜 Código de Conduta

Este projeto adere ao [Contributor Covenant](https://www.contributor-covenant.org/). Ao participar, você deve seguir este código.

## 🚀 Como Contribuir

### 1. Fork e Clone

```bash
# Fork o repositório no GitHub
# Clone seu fork
git clone https://github.com/SEU_USUARIO/SysDelivery.git
cd SysDelivery

# Adicione o repositório original como upstream
git remote add upstream https://github.com/nikolasdehor/SysDelivery.git
```

### 2. Configure o Ambiente

```bash
# Instale dependências
composer install

# Configure o ambiente
cp webserver/www/codeigniter4/.env.example webserver/www/codeigniter4/.env

# Inicie o ambiente de desenvolvimento
docker-compose up -d
```

### 3. Crie uma Branch

```bash
# Sempre crie uma nova branch para suas mudanças
git checkout -b feature/nova-funcionalidade
# ou
git checkout -b fix/correcao-bug
# ou
git checkout -b docs/atualizacao-documentacao
```

## 🛠️ Padrões de Desenvolvimento

### 📝 Padrões de Código

#### PHP
- Siga o padrão **PSR-12** para formatação
- Use **type hints** sempre que possível
- Documente métodos com **PHPDoc**
- Mantenha métodos pequenos e focados

```php
<?php

namespace App\Controllers;

/**
 * Controller para gerenciar produtos
 */
class ProdutosController extends BaseController
{
    /**
     * Lista produtos com paginação
     *
     * @param int $page Página atual
     * @return string
     */
    public function index(int $page = 1): string
    {
        // Implementação
    }
}
```

#### JavaScript
- Use **ES6+** features
- Prefira **const/let** ao invés de **var**
- Use **arrow functions** quando apropriado
- Documente funções complexas

```javascript
/**
 * Atualiza o carrinho via AJAX
 * @param {number} produtoId - ID do produto
 * @param {number} quantidade - Quantidade desejada
 */
const atualizarCarrinho = async (produtoId, quantidade) => {
    try {
        const response = await fetch('/carrinho/atualizar', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ produtoId, quantidade })
        });
        
        const data = await response.json();
        // Processar resposta
    } catch (error) {
        console.error('Erro ao atualizar carrinho:', error);
    }
};
```

#### SQL
- Use **nomes descritivos** para tabelas e colunas
- Sempre use **índices** em foreign keys
- Documente **constraints** complexas

```sql
-- Tabela para armazenar itens do carrinho
CREATE TABLE carrinho (
    carrinho_id INT NOT NULL AUTO_INCREMENT,
    carrinho_usuario_id INT NOT NULL,
    carrinho_produto_id INT NOT NULL,
    carrinho_quantidade INT NOT NULL DEFAULT 1,
    carrinho_preco_unitario DECIMAL(10,2) NOT NULL,
    carrinho_data_adicao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (carrinho_id),
    INDEX idx_carrinho_usuario (carrinho_usuario_id),
    INDEX idx_carrinho_produto (carrinho_produto_id),
    
    FOREIGN KEY (carrinho_usuario_id) 
        REFERENCES usuarios(usuarios_id) 
        ON DELETE CASCADE ON UPDATE CASCADE
);
```

### 🧪 Testes

#### Testes Unitários
```php
<?php

namespace Tests\Unit;

use CodeIgniter\Test\CIUnitTestCase;
use App\Models\Carrinho;

class CarrinhoTest extends CIUnitTestCase
{
    public function testAdicionarItem()
    {
        $carrinho = new Carrinho();
        
        $resultado = $carrinho->adicionarItem(1, 1, 2, 10.50);
        
        $this->assertTrue($resultado);
    }
}
```

#### Testes de Integração
```php
<?php

namespace Tests\Integration;

use CodeIgniter\Test\FeatureTestCase;

class CarrinhoIntegrationTest extends FeatureTestCase
{
    public function testAdicionarItemViaAPI()
    {
        $response = $this->post('/api/carrinho/adicionar', [
            'produto_id' => 1,
            'quantidade' => 2
        ]);
        
        $response->assertStatus(200);
        $response->assertJSONFragment(['success' => true]);
    }
}
```

### 📚 Documentação

- **Documente** todas as funções públicas
- **Atualize** o README.md se necessário
- **Inclua** exemplos de uso
- **Mantenha** a documentação da API atualizada

## 🔄 Processo de Pull Request

### 1. Antes de Submeter

```bash
# Certifique-se de que está atualizado
git fetch upstream
git checkout main
git merge upstream/main

# Execute os testes
php spark test

# Verifique o código
./vendor/bin/phpcs --standard=PSR12 app/
./vendor/bin/phpstan analyse app/
```

### 2. Commit Guidelines

Use o padrão **Conventional Commits**:

```bash
# Formato
tipo(escopo): descrição

# Exemplos
feat(carrinho): adiciona validação de estoque
fix(api): corrige erro de autenticação
docs(readme): atualiza instruções de instalação
style(css): melhora responsividade do menu
refactor(models): simplifica query de produtos
test(carrinho): adiciona testes unitários
```

### 3. Submeter PR

1. **Push** sua branch para seu fork
2. **Abra** um Pull Request no GitHub
3. **Preencha** o template de PR
4. **Aguarde** a revisão

#### Template de PR

```markdown
## 📝 Descrição
Breve descrição das mudanças realizadas.

## 🔄 Tipo de Mudança
- [ ] Bug fix (mudança que corrige um problema)
- [ ] Nova funcionalidade (mudança que adiciona funcionalidade)
- [ ] Breaking change (mudança que quebra compatibilidade)
- [ ] Documentação (mudança apenas na documentação)

## ✅ Checklist
- [ ] Meu código segue os padrões do projeto
- [ ] Realizei auto-revisão do código
- [ ] Comentei código complexo
- [ ] Adicionei testes que provam que a correção/funcionalidade funciona
- [ ] Testes novos e existentes passam
- [ ] Atualizei a documentação se necessário

## 🧪 Como Testar
Descreva como testar suas mudanças.

## 📸 Screenshots (se aplicável)
Adicione screenshots para mudanças visuais.
```

## 🐛 Reportar Bugs

Use o [template de bug report](https://github.com/nikolasdehor/SysDelivery/issues/new?template=bug_report.md):

```markdown
**Descreva o bug**
Descrição clara e concisa do problema.

**Para Reproduzir**
Passos para reproduzir o comportamento:
1. Vá para '...'
2. Clique em '....'
3. Role para baixo até '....'
4. Veja o erro

**Comportamento Esperado**
Descrição clara do que deveria acontecer.

**Screenshots**
Se aplicável, adicione screenshots.

**Ambiente:**
 - OS: [e.g. iOS]
 - Browser [e.g. chrome, safari]
 - Version [e.g. 22]

**Contexto Adicional**
Qualquer outra informação sobre o problema.
```

## 💡 Sugerir Funcionalidades

Use o [template de feature request](https://github.com/nikolasdehor/SysDelivery/issues/new?template=feature_request.md):

```markdown
**Sua solicitação de funcionalidade está relacionada a um problema?**
Descrição clara e concisa do problema.

**Descreva a solução que você gostaria**
Descrição clara e concisa do que você quer que aconteça.

**Descreva alternativas que você considerou**
Descrição de soluções ou funcionalidades alternativas.

**Contexto adicional**
Qualquer outra informação ou screenshots sobre a solicitação.
```

## 🏷️ Labels

Usamos as seguintes labels para organizar issues e PRs:

- `bug` - Algo não está funcionando
- `enhancement` - Nova funcionalidade ou solicitação
- `documentation` - Melhorias ou adições à documentação
- `good first issue` - Bom para iniciantes
- `help wanted` - Ajuda extra é bem-vinda
- `question` - Mais informações são solicitadas
- `wontfix` - Isso não será trabalhado

## 🎉 Reconhecimento

Contribuidores serão reconhecidos:

- **README.md** - Lista de contribuidores
- **CHANGELOG.md** - Créditos por versão
- **GitHub** - Contributor graph

## 📞 Dúvidas?

- Abra uma [issue](https://github.com/nikolasdehor/SysDelivery/issues)
- Entre no nosso [Discord](https://discord.gg/sysdelivery)
- Envie um email para: <EMAIL>

---

**Obrigado por contribuir! 🙏**
