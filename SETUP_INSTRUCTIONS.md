# 🚀 Instruções de Setup - SysDelivery Melhorado

## 📋 Pré-requisitos

- PHP 8.0 ou superior
- MySQL 5.7 ou superior
- Servidor web (Apache/Nginx)
- Composer (para dependências PHP)

## 🗄️ Configuração do Banco de Dados

### 1. <PERSON><PERSON><PERSON> o Banco de Dados
```sql
CREATE DATABASE sysdelivery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. Importar o Schema
```bash
mysql -u root -p sysdelivery < webserver/projeto.sql
```

### 3. Configurar Conexão
Edite o arquivo `webserver/www/codeigniter4/app/Config/Database.php`:

```php
public array $default = [
    'DSN'      => '',
    'hostname' => 'localhost',
    'username' => 'seu_usuario',
    'password' => 'sua_senha',
    'database' => 'sysdelivery',
    'DBDriver' => 'MySQLi',
    'DBPrefix' => '',
    'pConnect' => false,
    'DBDebug'  => true,
    'charset'  => 'utf8mb4',
    'DBCollat' => 'utf8mb4_unicode_ci',
    'swapPre'  => '',
    'encrypt'  => false,
    'compress' => false,
    'strictOn' => false,
    'failover' => [],
    'port'     => 3306,
];
```

## 🔧 Configuração do CodeIgniter

### 1. Configurar Base URL
Edite `webserver/www/codeigniter4/app/Config/App.php`:

```php
public string $baseURL = 'http://localhost/SysDelivery/webserver/www/codeigniter4/public/';
```

### 2. Configurar Ambiente
Edite `webserver/www/codeigniter4/.env`:

```env
CI_ENVIRONMENT = development

database.default.hostname = localhost
database.default.database = sysdelivery
database.default.username = seu_usuario
database.default.password = sua_senha
database.default.DBDriver = MySQLi
database.default.DBPrefix =
database.default.port = 3306
```

### 3. Configurar Segurança
No arquivo `.env`, adicione:

```env
# Chave de criptografia (gere uma nova)
encryption.key = sua_chave_de_32_caracteres_aqui

# Configurações de sessão
app.sessionDriver = 'CodeIgniter\Session\Handlers\FileHandler'
app.sessionCookieName = 'ci_session'
app.sessionExpiration = 7200
app.sessionSavePath = WRITEPATH . 'session'
app.sessionMatchIP = false
app.sessionTimeToUpdate = 300
app.sessionRegenerateDestroy = false

# CSRF Protection
security.csrfProtection = 'session'
security.tokenRandomize = true
security.tokenName = 'csrf_token_name'
security.headerName = 'X-CSRF-TOKEN'
security.cookieName = 'csrf_cookie_name'
security.expires = 7200
security.regenerate = true
security.redirect = true
```

## 📁 Estrutura de Diretórios

Certifique-se de que os diretórios tenham as permissões corretas:

```bash
chmod -R 755 webserver/www/codeigniter4/
chmod -R 777 webserver/www/codeigniter4/writable/
```

## 🔐 Configuração de Segurança

### 1. Gerar Chave de Criptografia
```bash
cd webserver/www/codeigniter4
php spark key:generate
```

### 2. Configurar HTTPS (Produção)
Para ambiente de produção, configure SSL/TLS no servidor web.

## 🚀 Inicialização

### 1. Servidor de Desenvolvimento
```bash
cd webserver/www/codeigniter4
php spark serve --host=0.0.0.0 --port=8080
```

### 2. Acessar o Sistema
- URL: `http://localhost:8080`
- Login padrão: Conforme dados no SQL

## 🧪 Testes

### 1. Verificar Funcionalidades Básicas
- [ ] Login/Logout
- [ ] Cadastro de usuários
- [ ] Listagem de produtos
- [ ] Carrinho de compras
- [ ] Sistema de avaliações
- [ ] Cupons de desconto
- [ ] Notificações

### 2. Testar API
```bash
# Listar produtos
curl -X GET http://localhost:8080/api/produtos \
  -H "Authorization: Bearer api_token_admin_123456"

# Validar cupom
curl -X POST http://localhost:8080/api/cupons/validar \
  -H "Content-Type: application/json" \
  -d '{"codigo": "BEMVINDO10", "valor_pedido": 50}'
```

## 🔧 Configurações Avançadas

### 1. Cache
Edite `app/Config/Cache.php` para configurar cache:

```php
public string $handler = 'file';
public int $ttl = 60;
public string $prefix = 'sysdelivery_';
```

### 2. Logs
Configure logs em `app/Config/Logger.php`:

```php
public int $threshold = 4; // 0=Emergencies, 4=All
```

### 3. Email (Para notificações)
Configure em `app/Config/Email.php`:

```php
public string $protocol = 'smtp';
public string $SMTPHost = 'smtp.gmail.com';
public int $SMTPPort = 587;
public string $SMTPUser = '<EMAIL>';
public string $SMTPPass = 'sua_senha_app';
public string $SMTPCrypto = 'tls';
```

## 🐛 Troubleshooting

### 1. Erro de Conexão com Banco
- Verifique credenciais no arquivo `.env`
- Confirme se o MySQL está rodando
- Teste conexão manual

### 2. Erro 404 nas Rotas
- Verifique configuração do `.htaccess`
- Confirme se mod_rewrite está habilitado
- Verifique a baseURL

### 3. Erro de Permissões
```bash
# Corrigir permissões
sudo chown -R www-data:www-data webserver/www/codeigniter4/
sudo chmod -R 755 webserver/www/codeigniter4/
sudo chmod -R 777 webserver/www/codeigniter4/writable/
```

### 4. Erro de Sessão
- Verifique se o diretório `writable/session` existe
- Confirme permissões de escrita
- Limpe sessões antigas se necessário

## 📊 Monitoramento

### 1. Logs do Sistema
- Aplicação: `writable/logs/`
- Servidor: `/var/log/apache2/` ou `/var/log/nginx/`
- MySQL: `/var/log/mysql/`

### 2. Performance
- Use ferramentas como New Relic ou DataDog
- Configure monitoramento de banco de dados
- Monitore uso de memória e CPU

## 🔄 Backup

### 1. Banco de Dados
```bash
# Backup
mysqldump -u root -p sysdelivery > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore
mysql -u root -p sysdelivery < backup_20240101_120000.sql
```

### 2. Arquivos
```bash
# Backup completo
tar -czf sysdelivery_backup_$(date +%Y%m%d_%H%M%S).tar.gz webserver/
```

## 🚀 Deploy em Produção

### 1. Configurações de Produção
```env
CI_ENVIRONMENT = production
app.forceGlobalSecureRequests = true
```

### 2. Otimizações
- Habilite cache de rotas
- Configure compressão gzip
- Use CDN para assets estáticos
- Configure cache de banco

### 3. Segurança
- Configure firewall
- Use HTTPS obrigatório
- Configure rate limiting no servidor
- Monitore logs de segurança

## 📞 Suporte

Para problemas ou dúvidas:
1. Verifique os logs em `writable/logs/`
2. Consulte a documentação do CodeIgniter 4
3. Verifique as configurações de ambiente

## ✅ Checklist de Validação

- [ ] Banco de dados criado e populado
- [ ] Configurações do CodeIgniter corretas
- [ ] Permissões de arquivo adequadas
- [ ] Servidor web configurado
- [ ] SSL/TLS configurado (produção)
- [ ] Backup configurado
- [ ] Monitoramento ativo
- [ ] Testes funcionais passando
- [ ] API funcionando
- [ ] Notificações funcionando
- [ ] Sistema de pagamentos testado

## 🎯 Próximos Passos

1. **Testes de Carga**: Teste o sistema com múltiplos usuários
2. **Otimização**: Ajuste performance baseado no uso real
3. **Monitoramento**: Configure alertas para problemas
4. **Documentação**: Mantenha documentação atualizada
5. **Treinamento**: Treine usuários nas novas funcionalidades
