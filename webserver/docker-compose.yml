version: '3.8'

services:
  apache:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: apache_server
    ports:
      - "8050:80"
    volumes:
      - ./www:/var/www/html
    working_dir: /var/www/html
    depends_on:
      - mysql
    networks:
      - app_net

  mysql:
    image: mysql:8.0
    container_name: mysql_server
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: SysDelivery
      MYSQL_USER: aluno
      MYSQL_PASSWORD: 123456
    ports:
      - "3356:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app_net

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: phpmyadmin_server
    ports:
      - "8051:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      MYSQL_ROOT_PASSWORD: root
    depends_on:
      - mysql
    networks:
      - app_net

volumes:
  mysql_data:

networks:
  app_net:
